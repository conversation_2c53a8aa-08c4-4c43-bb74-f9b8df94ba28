'use client';

import { useState, useEffect } from 'react';

export default function Home() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <nav className="glass fixed top-0 left-0 right-0 z-50 px-6 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="font-orbitron font-bold text-2xl text-glow">
            Build<span className="text-accent">Labz</span>
          </div>
          <div className="hidden md:flex space-x-8">
            <a href="#home" className="hover:text-accent transition-colors">Home</a>
            <a href="#services" className="hover:text-accent transition-colors">Services</a>
            <a href="#projects" className="hover:text-accent transition-colors">Projects</a>
            <a href="#team" className="hover:text-accent transition-colors">Team</a>
            <a href="#contact" className="hover:text-accent transition-colors">Contact</a>
          </div>
          <button className="btn-glow bg-accent text-black px-6 py-2 rounded-lg font-semibold">
            Get Started
          </button>
        </div>
      </nav>

      {/* Hero Section */}
      <section id="home" className="min-h-screen flex items-center justify-center px-6 pt-20">
        <div className="max-w-6xl mx-auto text-center">
          <div className="mb-8">
            <h1 className="font-orbitron font-bold text-6xl md:text-8xl mb-6 leading-tight">
              We <span className="text-accent text-glow flicker">Forge</span> the
              <br />
              Future of <span className="text-accent">Web3</span>
            </h1>
            <p className="text-xl md:text-2xl text-text-secondary mb-8 max-w-3xl mx-auto">
              One block at a time. From idea to reality in weeks, not months.
            </p>
          </div>

          <div className="flex flex-col md:flex-row gap-6 justify-center items-center mb-12">
            <button className="btn-glow bg-accent text-black px-8 py-4 rounded-lg font-bold text-lg mechanical-hover">
              Start Your Project
            </button>
            <button className="glass px-8 py-4 rounded-lg font-semibold text-lg mechanical-hover border border-accent">
              View Our Work
            </button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-16">
            <div className="glass p-6 rounded-lg mechanical-hover">
              <div className="font-orbitron font-bold text-3xl text-accent">50+</div>
              <div className="text-text-secondary">Projects Delivered</div>
            </div>
            <div className="glass p-6 rounded-lg mechanical-hover">
              <div className="font-orbitron font-bold text-3xl text-accent">24/7</div>
              <div className="text-text-secondary">Support</div>
            </div>
            <div className="glass p-6 rounded-lg mechanical-hover">
              <div className="font-orbitron font-bold text-3xl text-accent">100%</div>
              <div className="text-text-secondary">Success Rate</div>
            </div>
            <div className="glass p-6 rounded-lg mechanical-hover">
              <div className="font-orbitron font-bold text-3xl text-accent">5+</div>
              <div className="text-text-secondary">Years Experience</div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-20 px-6">
        <div className="max-w-6xl mx-auto">
          <h2 className="font-orbitron font-bold text-4xl md:text-6xl text-center mb-16">
            Our <span className="text-accent">Services</span>
          </h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="glass p-8 rounded-lg mechanical-hover neon-border">
              <div className="text-accent text-4xl mb-4">⚡</div>
              <h3 className="font-orbitron font-bold text-xl mb-4">DApp Development</h3>
              <p className="text-text-secondary">Build decentralized applications that revolutionize industries.</p>
            </div>

            <div className="glass p-8 rounded-lg mechanical-hover neon-border">
              <div className="text-accent text-4xl mb-4">🔗</div>
              <h3 className="font-orbitron font-bold text-xl mb-4">Smart Contracts</h3>
              <p className="text-text-secondary">Secure, audited smart contracts for EVM, Solana, and more.</p>
            </div>

            <div className="glass p-8 rounded-lg mechanical-hover neon-border">
              <div className="text-accent text-4xl mb-4">🤖</div>
              <h3 className="font-orbitron font-bold text-xl mb-4">Telegram Bots</h3>
              <p className="text-text-secondary">Automated trading bots and community management tools.</p>
            </div>

            <div className="glass p-8 rounded-lg mechanical-hover neon-border">
              <div className="text-accent text-4xl mb-4">🎮</div>
              <h3 className="font-orbitron font-bold text-xl mb-4">Game Development</h3>
              <p className="text-text-secondary">Unity/Web3 integration for blockchain gaming experiences.</p>
            </div>

            <div className="glass p-8 rounded-lg mechanical-hover neon-border">
              <div className="text-accent text-4xl mb-4">🏗️</div>
              <h3 className="font-orbitron font-bold text-xl mb-4">Web3 Infrastructure</h3>
              <p className="text-text-secondary">Complete blockchain infrastructure setup and management.</p>
            </div>

            <div className="glass p-8 rounded-lg mechanical-hover neon-border">
              <div className="text-accent text-4xl mb-4">🎨</div>
              <h3 className="font-orbitron font-bold text-xl mb-4">UI/UX Design</h3>
              <p className="text-text-secondary">Futuristic interfaces that users love to interact with.</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-6">
        <div className="max-w-4xl mx-auto text-center">
          <div className="glass-strong p-12 rounded-2xl">
            <h2 className="font-orbitron font-bold text-4xl md:text-5xl mb-6">
              Ready to Build the <span className="text-accent text-glow">Future</span>?
            </h2>
            <p className="text-xl text-text-secondary mb-8">
              Join the Web3 revolution. Let's create something extraordinary together.
            </p>
            <button className="btn-glow bg-accent text-black px-12 py-4 rounded-lg font-bold text-xl mechanical-hover">
              Start Your Journey
            </button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="glass py-12 px-6 mt-20">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="font-orbitron font-bold text-2xl text-glow mb-4">
                Build<span className="text-accent">Labz</span>
              </div>
              <p className="text-text-secondary">
                Forging the future of Web3, one block at a time.
              </p>
            </div>

            <div>
              <h4 className="font-orbitron font-bold mb-4">Services</h4>
              <ul className="space-y-2 text-text-secondary">
                <li><a href="#" className="hover:text-accent transition-colors">DApp Development</a></li>
                <li><a href="#" className="hover:text-accent transition-colors">Smart Contracts</a></li>
                <li><a href="#" className="hover:text-accent transition-colors">Web3 Infrastructure</a></li>
              </ul>
            </div>

            <div>
              <h4 className="font-orbitron font-bold mb-4">Company</h4>
              <ul className="space-y-2 text-text-secondary">
                <li><a href="#" className="hover:text-accent transition-colors">About</a></li>
                <li><a href="#" className="hover:text-accent transition-colors">Team</a></li>
                <li><a href="#" className="hover:text-accent transition-colors">Careers</a></li>
              </ul>
            </div>

            <div>
              <h4 className="font-orbitron font-bold mb-4">Connect</h4>
              <ul className="space-y-2 text-text-secondary">
                <li><a href="#" className="hover:text-accent transition-colors">Twitter</a></li>
                <li><a href="#" className="hover:text-accent transition-colors">Discord</a></li>
                <li><a href="#" className="hover:text-accent transition-colors">Telegram</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-primary-600 mt-8 pt-8 text-center text-text-secondary">
            <p>&copy; 2024 BuildLabz. All rights reserved. Built with ⚡ and 🔥</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
