@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Share+Tech+Mono&family=Titillium+Web:wght@300;400;600;700&display=swap');

/* BuildLabz Frankenstein Futurism Theme */
:root {
  --background: #000000;
  --foreground: #ffffff;
  --primary-deep: #0c2f27;
  --primary-muted: #1a4035;
  --primary-teal: #295247;
  --accent-neon: #00FF9E;
  --text-secondary: #cfcfcf;
  --glass: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  background: var(--background);
  color: var(--foreground);
  font-family: 'Titillium Web', sans-serif;
  scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-teal);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-neon);
}

/* Glassmorphism utility classes */
.glass {
  background: var(--glass);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
}

.glass-strong {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Glow effects */
.glow-accent {
  box-shadow: 0 0 20px rgba(0, 255, 158, 0.3);
}

.glow-accent-strong {
  box-shadow: 0 0 40px rgba(0, 255, 158, 0.5);
}

/* Text glow */
.text-glow {
  text-shadow: 0 0 10px var(--accent-neon);
}

/* Button hover effects */
.btn-glow {
  transition: all 0.3s ease;
}

.btn-glow:hover {
  box-shadow: 0 0 20px rgba(0, 255, 158, 0.4);
  transform: translateY(-2px);
}

/* Animated background blobs */
.blob {
  border-radius: 50%;
  filter: blur(40px);
  animation: blob 7s infinite;
}

.blob-1 {
  background: linear-gradient(45deg, var(--primary-deep), var(--primary-teal));
}

.blob-2 {
  background: linear-gradient(45deg, var(--primary-teal), var(--accent-neon));
  animation-delay: -2s;
}

.blob-3 {
  background: linear-gradient(45deg, var(--accent-neon), var(--primary-muted));
  animation-delay: -4s;
}

/* Flicker animation for text */
.flicker {
  animation: flicker 1.5s infinite linear;
}

/* Mechanical/alive feel animations */
.mechanical-hover {
  transition: all 0.2s ease;
}

.mechanical-hover:hover {
  transform: scale(1.02);
  filter: brightness(1.1);
}

/* Grid pattern overlay */
.grid-pattern {
  background-image:
    linear-gradient(rgba(0, 255, 158, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 158, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Neon border effect */
.neon-border {
  border: 1px solid var(--accent-neon);
  box-shadow:
    0 0 5px var(--accent-neon),
    inset 0 0 5px var(--accent-neon);
}

/* Loading spinner */
.spinner {
  border: 2px solid var(--primary-teal);
  border-top: 2px solid var(--accent-neon);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
