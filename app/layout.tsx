import type { Metada<PERSON> } from "next";
import { Or<PERSON>ron, Share_Tech_Mono, Titillium_Web } from "next/font/google";
import "./globals.css";

const orbitron = Orbitron({
  variable: "--font-orbitron",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700", "800", "900"],
});

const shareTechMono = Share_Tech_Mono({
  variable: "--font-share-tech",
  subsets: ["latin"],
  weight: ["400"],
});

const titilliumWeb = Titillium_Web({
  variable: "--font-titillium",
  subsets: ["latin"],
  weight: ["300", "400", "600", "700"],
});

export const metadata: Metadata = {
  title: "BuildLabz - Forge the Future of Web3",
  description: "We forge the future of Web3. One block at a time. Web3 development studio specializing in blockchain-based solutions, DApp development, smart contracts, and Web3 infrastructure.",
  keywords: ["Web3", "Blockchain", "DApp", "Smart Contracts", "BuildLabz", "Crypto", "Development"],
  authors: [{ name: "BuildLabz Team" }],
  viewport: "width=device-width, initial-scale=1",
  themeColor: "#000000",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body
        className={`${orbitron.variable} ${shareTechMono.variable} ${titilliumWeb.variable} antialiased bg-background text-foreground font-titillium`}
      >
        {/* Animated Background Blobs */}
        <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
          <div className="blob blob-1 absolute top-1/4 left-1/4 w-64 h-64 opacity-20"></div>
          <div className="blob blob-2 absolute top-3/4 right-1/4 w-96 h-96 opacity-15"></div>
          <div className="blob blob-3 absolute bottom-1/4 left-1/2 w-80 h-80 opacity-10"></div>
        </div>

        {/* Grid Pattern Overlay */}
        <div className="fixed inset-0 grid-pattern opacity-5 pointer-events-none z-0"></div>

        {/* Main Content */}
        <div className="relative z-10">
          {children}
        </div>
      </body>
    </html>
  );
}
