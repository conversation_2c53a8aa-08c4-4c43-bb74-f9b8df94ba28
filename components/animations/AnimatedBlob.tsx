'use client';

import { HTMLAttributes } from 'react';
import { cn } from '@/lib/utils';

interface AnimatedBlobProps extends HTMLAttributes<HTMLDivElement> {
  variant?: 1 | 2 | 3;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  opacity?: number;
}

export function AnimatedBlob({ 
  className, 
  variant = 1, 
  size = 'md', 
  opacity = 0.2,
  ...props 
}: AnimatedBlobProps) {
  const sizes = {
    sm: 'w-32 h-32',
    md: 'w-64 h-64',
    lg: 'w-80 h-80',
    xl: 'w-96 h-96',
  };

  const variants = {
    1: 'blob-1',
    2: 'blob-2',
    3: 'blob-3',
  };

  return (
    <div
      className={cn(
        'blob absolute',
        sizes[size],
        variants[variant],
        className
      )}
      style={{ opacity }}
      {...props}
    />
  );
}
