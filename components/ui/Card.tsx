'use client';

import { HTMLAttributes, forwardRef } from 'react';
import { cn } from '@/lib/utils';

interface CardProps extends HTMLAttributes<HTMLDivElement> {
  variant?: 'glass' | 'glass-strong' | 'neon' | 'solid';
  hover?: boolean;
  children: React.ReactNode;
}

const Card = forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant = 'glass', hover = true, children, ...props }, ref) => {
    const baseClasses = 'rounded-lg p-6';
    
    const variants = {
      glass: 'glass',
      'glass-strong': 'glass-strong',
      neon: 'glass neon-border',
      solid: 'bg-primary-800 border border-primary-600',
    };
    
    const hoverClasses = hover ? 'mechanical-hover' : '';

    return (
      <div
        className={cn(
          baseClasses,
          variants[variant],
          hoverClasses,
          className
        )}
        ref={ref}
        {...props}
      >
        {children}
      </div>
    );
  }
);

Card.displayName = 'Card';

export { Card };
export type { CardProps };
