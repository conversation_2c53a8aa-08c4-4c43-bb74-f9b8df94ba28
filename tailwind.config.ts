import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        // BuildLabz Frankenstein Futurism Theme
        background: "#000000", // Pure Black
        foreground: "#ffffff", // Bright White
        
        // Primary Accents (Muted Green Palette)
        primary: {
          50: "#f0f9f6",
          100: "#dcf2e8",
          200: "#bce5d4",
          300: "#8dd1b8",
          400: "#5ab896",
          500: "#369d78",
          600: "#295247", // Dark Teal Green
          700: "#1a4035", // Muted Forest
          800: "#0c2f27", // Deep Matte Green
          900: "#051a15",
        },
        
        // Electric Neon Green Highlight
        accent: "#00FF9E",
        
        // Typography Colors
        text: {
          primary: "#ffffff",
          secondary: "#cfcfcf", // Subtle Gray
          muted: "#9ca3af",
        },
        
        // Glass/Blur Effects
        glass: "rgba(255, 255, 255, 0.1)",
        "glass-border": "rgba(255, 255, 255, 0.2)",
      },
      
      fontFamily: {
        orbitron: ["Orbitron", "sans-serif"],
        "share-tech": ["Share Tech Mono", "monospace"],
        titillium: ["Titillium Web", "sans-serif"],
      },
      
      animation: {
        "blob": "blob 7s infinite",
        "pulse-glow": "pulse-glow 2s ease-in-out infinite alternate",
        "flicker": "flicker 1.5s infinite linear",
        "spark": "spark 0.3s ease-out",
        "float": "float 6s ease-in-out infinite",
        "glitch": "glitch 0.3s ease-in-out",
      },
      
      keyframes: {
        blob: {
          "0%": {
            transform: "translate(0px, 0px) scale(1)",
          },
          "33%": {
            transform: "translate(30px, -50px) scale(1.1)",
          },
          "66%": {
            transform: "translate(-20px, 20px) scale(0.9)",
          },
          "100%": {
            transform: "translate(0px, 0px) scale(1)",
          },
        },
        "pulse-glow": {
          "0%": {
            boxShadow: "0 0 5px #00FF9E, 0 0 10px #00FF9E, 0 0 15px #00FF9E",
          },
          "100%": {
            boxShadow: "0 0 10px #00FF9E, 0 0 20px #00FF9E, 0 0 30px #00FF9E",
          },
        },
        flicker: {
          "0%, 19.999%, 22%, 62.999%, 64%, 64.999%, 70%, 100%": {
            opacity: "0.99",
          },
          "20%, 21.999%, 63%, 63.999%, 65%, 69.999%": {
            opacity: "0.4",
          },
        },
        spark: {
          "0%": {
            transform: "scale(0) rotate(0deg)",
            opacity: "1",
          },
          "100%": {
            transform: "scale(1) rotate(180deg)",
            opacity: "0",
          },
        },
        float: {
          "0%, 100%": {
            transform: "translateY(0px)",
          },
          "50%": {
            transform: "translateY(-20px)",
          },
        },
        glitch: {
          "0%": {
            transform: "translate(0)",
          },
          "20%": {
            transform: "translate(-2px, 2px)",
          },
          "40%": {
            transform: "translate(-2px, -2px)",
          },
          "60%": {
            transform: "translate(2px, 2px)",
          },
          "80%": {
            transform: "translate(2px, -2px)",
          },
          "100%": {
            transform: "translate(0)",
          },
        },
      },
      
      backdropBlur: {
        xs: "2px",
      },
      
      boxShadow: {
        "glow": "0 0 20px rgba(0, 255, 158, 0.3)",
        "glow-lg": "0 0 40px rgba(0, 255, 158, 0.4)",
        "inner-glow": "inset 0 0 20px rgba(0, 255, 158, 0.2)",
      },
    },
  },
  plugins: [],
};
export default config;
